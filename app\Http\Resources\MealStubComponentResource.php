<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class MealStubComponentResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'REFERENCEID' => $this->REFERENCEID,
            'PRODUCTID' => $this->PRODUCTID,
            'PRODUCTDESC' => $this->PRODUCTDESC,
            'QTY' => $this->QTY,
            'LINENO' => $this->LINENO,
            'BRANCHID' => $this->BRANCHID,
            'OUTLETID' => $this->OUTLETID
        ];
    }
}
