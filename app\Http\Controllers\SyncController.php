<?php

namespace App\Http\Controllers;

use App\Http\Controllers\Controller;
use App\Http\Resources\InParkCurrencyDetailsResource;
use App\Http\Resources\MealStubComponentResource;
use App\Http\Requests\SendToServerRequest;
use App\Helpers\ApiResponse;
use App\Models\InParkCurrencyDetails;
use App\Models\MealStubComponents;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class SyncController extends Controller
{
    /**
     * GET /api/meal-stub/{invoiceNo}/{outletId}/{posNo}
     * Kukunin ko ang 
     * 

     */
    public function getMealStubFromPOS($referenceId, Request $request)
    {
        // Find InParkCurrencyDetails record by REFERENCE_ID
        $inParkRecord = InParkCurrencyDetails::where('REFERENCE_ID', $referenceId)->first();

        // Find MealStubComponents by REFERENCEID
        $mealStubComponents = MealStubComponents::where('REFERENCEID', $referenceId)->get();

        if ($mealStubComponents->isEmpty() && !$inParkRecord) {
            return ApiResponse::error('No data found for this reference ID', 404, [
                'reference_id' => $referenceId
            ]);
        }

        return ApiResponse::success('Success', [
            'INPARKCURRENCYDETAILS' => $inParkRecord ? new InParkCurrencyDetailsResource($inParkRecord) : null,
            'MEALSTUBCOMPONENTS' => MealStubComponentResource::collection($mealStubComponents),
            // 'MEALSTUB_DEBUG_INFO' => [
            //     'searched_reference_id' => $referenceId,
            //     'inpark_reference_id' => $inParkRecord ? $inParkRecord->REFERENCE_ID : null,
            //     'mealstub_count' => $mealStubComponents->count()
            // ]
        ]);
    }

    /**
     * POST /api/send-to-server
     * UPSERT sa mga data sa target database.
     */
    public function sendToServer(SendToServerRequest $request)
    {
        $payloads = $request->validated();
        $results = [];

        DB::connection('sqlsrv')->transaction(function () use ($payloads, &$results) {
            foreach ($payloads as $item) {
                $table = $item['table'];
                $data = collect($item['data'])->pluck('value', 'column')->toArray();
                $conditions = collect($item['conditions'])->pluck('value', 'column')->toArray();
                $autoUpdate = (bool) $item['auto_update']; // ensure boolean

                try {
                    if ($autoUpdate) {
                        DB::connection('sqlsrv')
                            ->table($table)
                            ->updateOrInsert($conditions, $data);
                    } else {
                        DB::connection('sqlsrv')
                            ->table($table)
                            ->insert($data);
                    }

                    $results[] = [
                        'StatusCode' => 200,
                        'Message' => 'Success',
                        'Data' => $conditions
                    ];
                } catch (\Throwable $e) {
                    $results[] = [
                        'StatusCode' => 500,
                        'Message' => $e->getMessage(),
                        'Data' => $conditions
                    ];
                }
            }
        });

        return ApiResponse::success('Data processed successfully', $results);
    }



}
