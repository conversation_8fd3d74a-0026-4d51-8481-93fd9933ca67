<?php

namespace App\Http\Controllers;

use App\Http\Controllers\Controller;
use App\Http\Resources\InParkCurrencyDetailsResource;
use App\Http\Resources\MealStubComponentResource;
use App\Http\Requests\SendToServerRequest;
use App\Helpers\ApiResponse;
use App\Models\InParkCurrencyDetails;
use App\Models\MealStubComponents;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class SyncController extends Controller
{
    /**
     * GET /api/meal-stub/{invoiceNo}/{outletId}/{posNo}
     * Kukunin ung MealStubComponents sa POS na may same Invoice No, Outlet ID at POS Number.
     * 

     */
    public function getMealStubFromPOS($branchId, $invoiceNo, $outletId, $posNo, Request $request)
    {
        // Get optional rowguid parameter for specific filtering
        $rowguid = $request->query('rowguid');

        // Find InParkCurrencyDetails record
        $inParkQuery = InParkCurrencyDetails::where([
            'BRANCHID' => $branchId,
            'INVNO' => $invoiceNo,
            'OUTLETID' => $outletId,
            'POSNO' => $posNo
        ]);

        // Add rowguid filter if provided
        if ($rowguid) {
            $inParkQuery->where('rowguid', $rowguid);
        }

        $inParkRecord = $inParkQuery->firstOrFail();

        // Find MealStubComponents using the correct relationship
        // Based on your SQL: JOIN with PRODUCTDESC LIKE pattern
        $mealStubQuery = MealStubComponents::where('PRODUCTDESC', 'LIKE', 'FREE HOTDOG ON STICK%');

        // You can also add additional filters if needed
        // $mealStubQuery->where('BRANCHID', $branchId);
        // $mealStubQuery->where('OUTLETID', $outletId);

        $mealStubComponents = $mealStubQuery->get();

        if ($mealStubComponents->isEmpty()) {
            return ApiResponse::error('No meal stub components found', 404, [
                'search_parameters' => [
                    'BRANCHID' => $branchId,
                    'INVNO' => $invoiceNo,
                    'OUTLETID' => $outletId,
                    'POSNO' => $posNo,
                    'rowguid' => $rowguid,
                    'product_pattern' => 'FREE HOTDOG ON STICK%'
                ]
            ]);
        }

        return ApiResponse::success('Success', [
            'INPARKCURRENCYDETAILS' => new InParkCurrencyDetailsResource($inParkRecord),
            'MEALSTUBCOMPONENTS' => MealStubComponentResource::collection($mealStubComponents)
        ]);
    }

    /**
     * POST /api/send-to-server
     * UPSERT sa mga data sa target database.
     */
    public function sendToServer(SendToServerRequest $request)
    {
        $payloads = $request->validated();
        $results = [];

        DB::connection('sqlsrv')->transaction(function () use ($payloads, &$results) {
            foreach ($payloads as $item) {
                $table = $item['table'];
                $data = collect($item['data'])->pluck('value', 'column')->toArray();
                $conditions = collect($item['conditions'])->pluck('value', 'column')->toArray();
                $autoUpdate = (bool) $item['auto_update']; // ensure boolean

                try {
                    if ($autoUpdate) {
                        DB::connection('sqlsrv')
                            ->table($table)
                            ->updateOrInsert($conditions, $data);
                    } else {
                        DB::connection('sqlsrv')
                            ->table($table)
                            ->insert($data);
                    }

                    $results[] = [
                        'StatusCode' => 200,
                        'Message' => 'Success',
                        'Data' => $conditions
                    ];
                } catch (\Throwable $e) {
                    $results[] = [
                        'StatusCode' => 500,
                        'Message' => $e->getMessage(),
                        'Data' => $conditions
                    ];
                }
            }
        });

        return ApiResponse::success('Data processed successfully', $results);
    }



}
