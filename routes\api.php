<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\SyncController;

// Test endpoint without auth
Route::get('/test', function () {
    return response()->json(['message' => 'API is working!', 'timestamp' => now()]);
});

Route::middleware(['basic.auth'])->group(function () {
    Route::get('/meal-stub/{referenceId}', [SyncController::class, 'getMealStubFromPOS']);
    Route::post('/send-to-server', [SyncController::class, 'sendToServer']);
});



